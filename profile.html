<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast - Profile</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #1f1f1f;
            width: 428px;
            height: 926px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 48px 32px 0;
            position: relative;
            z-index: 10;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 50px;
            height: 46px;
        }

        .logo-text {
            width: 157px;
            height: 25px;
        }

        .settings-btn {
            width: 48px;
            height: 48px;
            background: rgba(31, 31, 31, 0.1);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
        }

        .settings-icon {
            width: 21px;
            height: 21px;
        }

        .profile-section {
            padding: 0 32px;
            margin-top: 32px;
        }

        .profile-avatar-container {
            display: flex;
            justify-content: center;
            margin-bottom: -100px;
            z-index: 15;
            position: relative;
        }

        .profile-avatar {
            width: 200px;
            height: 200px;
            border-radius: 100px;
            border: 8px solid #ffffff;
            object-fit: cover;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .profile-bg {
            width: 364px;
            height: 285px;
            background: #4c0099;
            border-radius: 24px;
            position: relative;
            overflow: visible;
            margin: 0 auto;
            padding-top: 100px;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
        }

        .profile-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(87, 58, 255, 0.16);
            border-radius: 24px;
        }

        .profile-info {
            text-align: center;
            color: white;
            padding: 20px 0 40px 0;
            position: relative;
            z-index: 10;
        }

        .profile-name {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 32px;
            line-height: 38px;
        }

        .profile-stats {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 40px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            line-height: 28px;
        }

        .stat-label {
            font-size: 16px;
            opacity: 0.7;
            line-height: 19px;
        }

        .stat-divider {
            width: 1px;
            height: 54px;
            background: rgba(255, 255, 255, 0.3);
        }

        .recently-played {
            padding: 32px;
            margin-top: 32px;
            padding-bottom: 120px;
        }

        .section-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 32px;
            line-height: 28px;
        }

        .podcast-item {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 24px;
            position: relative;
        }

        .podcast-cover {
            width: 108px;
            height: 96px;
            border-radius: 16px;
            object-fit: cover;
        }

        .podcast-info {
            flex: 1;
        }

        .podcast-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
            line-height: 19px;
        }

        .podcast-duration {
            font-size: 14px;
            opacity: 0.7;
            margin-bottom: 16px;
            line-height: 16px;
        }

        .progress-bar {
            width: 176px;
            height: 8px;
            background: #e9e9e9;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #ff5757;
            border-radius: 4px;
        }

        .play-btn {
            width: 48px;
            height: 48px;
            background: rgba(76, 0, 153, 0.1);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
        }

        .play-icon {
            width: 18px;
            height: 18px;
        }

        .bottom-nav {
            position: fixed;
            bottom: 32px;
            left: 50%;
            transform: translateX(-50%);
            width: 364px;
            height: 72px;
            background: rgba(76, 0, 153, 0.1);
            border-radius: 48px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 0 20px;
            z-index: 20;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            opacity: 0.5;
            cursor: pointer;
        }

        .nav-icon.active {
            opacity: 1;
        }

        .nav-indicator {
            position: absolute;
            right: 58px;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 5px;
            background: #4c0099;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo-container">
            <img src="images/ncast-icon.png" alt="NCast Icon" class="logo-icon">
            <img src="images/ncast-logo.png" alt="NCast" class="logo-text">
        </div>
        <button class="settings-btn">
            <img src="images/gear-icon.png" alt="Settings" class="settings-icon">
        </button>
    </div>

    <div class="profile-section">
        <div class="profile-avatar-container">
            <img src="images/profile-avatar.png" alt="Naveen Prasath" class="profile-avatar">
        </div>
        <div class="profile-bg">
            <div class="profile-info">
                <div class="profile-name">Naveen Prasath</div>
                <div class="profile-stats">
                    <div class="stat-item">
                        <div class="stat-number">22</div>
                        <div class="stat-label">Liked Podcasts</div>
                    </div>
                    <div class="stat-divider"></div>
                    <div class="stat-item">
                        <div class="stat-number">158</div>
                        <div class="stat-label">Following</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="recently-played">
        <h2 class="section-title">Recently Played</h2>

        <div class="podcast-item">
            <img src="images/podcast1-cover.png" alt="True Crime" class="podcast-cover">
            <div class="podcast-info">
                <div class="podcast-title">True Crime</div>
                <div class="podcast-duration">20 min remaining</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 33.5%;"></div>
                </div>
            </div>
            <button class="play-btn">
                <img src="images/play-icon.png" alt="Play" class="play-icon">
            </button>
        </div>

        <div class="podcast-item">
            <img src="images/podcast2-cover.png" alt="See Radio" class="podcast-cover">
            <div class="podcast-info">
                <div class="podcast-title">See Radio</div>
                <div class="podcast-duration">35 min remaining</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 65.9%;"></div>
                </div>
            </div>
            <button class="play-btn">
                <img src="images/play-icon2.png" alt="Play" class="play-icon">
            </button>
        </div>
    </div>

    <div class="bottom-nav">
        <img src="images/headphones-icon.png" alt="Headphones" class="nav-icon">
        <img src="images/compass-icon.png" alt="Compass" class="nav-icon">
        <img src="images/heart-icon.png" alt="Heart" class="nav-icon">
        <img src="images/profile-icon.png" alt="Profile" class="nav-icon active">
        <div class="nav-indicator"></div>
    </div>
</body>
</html>
