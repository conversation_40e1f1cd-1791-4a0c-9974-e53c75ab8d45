<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast - Trending Podcasts</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #1f1f1f;
            width: 428px;
            height: 926px;
            margin: 0 auto;
            position: relative;
        }

        .container {
            padding: 24px 32px;
            height: 100%;
        }

        /* Header */
        .header {
            display: flex;
            align-items: center;
            margin-bottom: 22px;
            position: relative;
        }

        .back-button {
            width: 48px;
            height: 48px;
            border: none;
            cursor: pointer;
            margin-right: 24px;
            background-color: rgba(31, 31, 31, 0.1);
            border-radius: 50%;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-button img {
            width: 20px;
            height: 20px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f1f1f;
        }

        /* Search Box */
        .search-container {
            margin-bottom: 32px;
        }

        .search-box {
            width: 364px;
            height: 64px;
            background-color: rgba(31, 31, 31, 0.08);
            border-radius: 32px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            border: none;
        }

        .search-icon {
            width: 24px;
            height: 24px;
            margin-right: 16px;
            position: relative;
        }

        .search-icon::before {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            border: 2px solid #000;
            border-radius: 50%;
            top: 2px;
            left: 2px;
        }

        .search-icon::after {
            content: '';
            position: absolute;
            width: 5px;
            height: 2px;
            background-color: #000;
            transform: rotate(45deg);
            bottom: 2px;
            right: 2px;
        }

        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            font-size: 16px;
            color: rgba(31, 31, 31, 0.5);
            outline: none;
        }

        /* Podcast Grid */
        .podcast-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 32px;
        }

        .podcast-item {
            text-align: left;
        }

        .podcast-image {
            width: 174px;
            height: 150px;
            border-radius: 16px;
            object-fit: cover;
            margin-bottom: 16px;
        }

        .podcast-title {
            font-size: 16px;
            font-weight: 500;
            color: #1f1f1f;
            margin-bottom: 4px;
            line-height: 19px;
        }

        .podcast-category {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
            line-height: 16px;
        }

        /* Recommended Section */
        .recommended-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f1f1f;
            margin-bottom: 24px;
        }

        .recommended-list {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .recommended-item {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .recommended-image {
            width: 108px;
            height: 96px;
            border-radius: 16px;
            object-fit: cover;
            flex-shrink: 0;
        }

        .recommended-content {
            flex: 1;
        }

        .recommended-podcast-title {
            font-size: 16px;
            font-weight: 500;
            color: #1f1f1f;
            margin-bottom: 8px;
            line-height: 19px;
        }

        .recommended-category {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
            margin-bottom: 8px;
            line-height: 16px;
        }

        .recommended-duration {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
            line-height: 16px;
        }

        .play-button {
            width: 48px;
            height: 48px;
            border: none;
            cursor: pointer;
            flex-shrink: 0;
            background-color: rgba(76, 0, 153, 0.1);
            border-radius: 24px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .play-button img {
            width: 18px;
            height: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <button class="back-button">
                <img src="images/back-button-full.png" alt="Back">
            </button>
            <h1 class="page-title">Trending Podcasts</h1>
        </div>

        <!-- Search Box -->
        <div class="search-container">
            <div class="search-box">
                <div class="search-icon"></div>
                <input type="text" class="search-input" placeholder="Search the podcast here...">
            </div>
        </div>

        <!-- Podcast Grid -->
        <div class="podcast-grid">
            <div class="podcast-item">
                <img src="images/podcast1.png" alt="Mind of an Entrepreneur" class="podcast-image">
                <div class="podcast-title">Mind of an Entrepre...</div>
                <div class="podcast-category">Business</div>
            </div>
            <div class="podcast-item">
                <img src="images/podcast3.png" alt="Unravelling the Mind" class="podcast-image">
                <div class="podcast-title">Unravelling the Mind</div>
                <div class="podcast-category">Healthy Lifestyle</div>
            </div>
            <div class="podcast-item">
                <img src="images/podcast4.png" alt="A Tale of Writer" class="podcast-image">
                <div class="podcast-title">A Tale of Writer</div>
                <div class="podcast-category">Educational</div>
            </div>
            <div class="podcast-item">
                <img src="images/podcast2.png" alt="Addiction to Social!" class="podcast-image">
                <div class="podcast-title">Addiction to Social!</div>
                <div class="podcast-category">Sociology</div>
            </div>
        </div>

        <!-- Recommended Section -->
        <h2 class="recommended-title">Recommended For You</h2>
        <div class="recommended-list">
            <div class="recommended-item">
                <img src="images/podcast5.png" alt="Mind map" class="recommended-image">
                <div class="recommended-content">
                    <div class="recommended-podcast-title">Mind map</div>
                    <div class="recommended-category">Health & Lifestyle</div>
                    <div class="recommended-duration">10 min</div>
                </div>
                <button class="play-button">
                    <img src="images/play-button1.png" alt="Play">
                </button>
            </div>
            <div class="recommended-item">
                <img src="images/podcast6.png" alt="True Crime" class="recommended-image">
                <div class="recommended-content">
                    <div class="recommended-podcast-title">True Crime</div>
                    <div class="recommended-category">Investigation Theories</div>
                    <div class="recommended-duration">15 min</div>
                </div>
                <button class="play-button">
                    <img src="images/play-button2.png" alt="Play">
                </button>
            </div>
        </div>
    </div>
</body>
</html>
